import asyncio
import random
import os
from typing import List, Dict, Any
from agents import Agent, Runner, ItemHelpers, function_tool

# Enhanced imports for Gemini integration
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    print("⚠️  google-generativeai not installed. Install with: pip install google-generativeai")

# ────────────────────────────────────────────────────────────────
# 📦 TOOL 1: Decide how many jokes to tell
@function_tool
def how_many_jokes():
    """Decide a random number of jokes to tell (between 1 and 3)."""
    return random.randint(1, 3)

# 📦 TOOL 2: Generate N jokes about a topic
@function_tool
def tell_jokes(topic: str, count: int):
    """Return a list of jokes about the given topic."""
    all_jokes = {
        "tech": [
            "Why do programmers prefer dark mode? Because light attracts bugs!",
            "There are only 10 types of people in the world: those who understand binary and those who don’t.",
            "Why was the developer unhappy at their job? They wanted arrays."
        ],
        "science": [
            "Why can't you trust an atom? Because they make up everything!",
            "I told a chemistry joke once... there was no reaction.",
            "Why did the photon check a bag? It didn’t – it was traveling light."
        ]
    }
    jokes = all_jokes.get(topic.lower(), ["No jokes available for this topic."])
    return jokes[:count]

# ────────────────────────────────────────────────────────────────
# 🤖 AGENT DEFINITION
agent = Agent(
    name="joke_master",
    instructions=(
        "You're a humorous assistant. "
        "First, use 'how_many_jokes' to decide how many jokes to tell. "
        "Then use 'tell_jokes' to tell that many jokes about 'tech'."
    ),
    tools=[how_many_jokes, tell_jokes],
)

# ────────────────────────────────────────────────────────────────
# 🔄 MAIN EVENT LOOP
async def main():
    print("\n🚀 AI Agent with Multi-Tool Chaining and Streaming\n")
    print("🔧 Tools Registered:", [tool.name for tool in agent.tools])
    print("🧠 Instructions:", agent.instructions)

    try:
        result = Runner.run_streamed(agent, input="Let's hear some tech jokes!")

        async for event in result.stream_events():
            event_type = type(event).__name__
            print(f"\n📡 Event: {event_type}")

            if hasattr(event, 'item'):
                item_type = event.item.type
                print(f"   └─ Item Type: {item_type}")

                if item_type == "tool_call_output_item":
                    print(f"   └─ 🔧 Tool Output: {event.item.output}")
                elif item_type == "message_output_item":
                    message = ItemHelpers.text_message_output(event.item)
                    print(f"   └─ 💬 AI Message:\n{message}")
            else:
                print("   └─ Other event structure (not an item)")

    except Exception as e:
        print(f"\n❌ Error: {type(e).__name__} - {str(e)}")

# ────────────────────────────────────────────────────────────────
# 🟢 ENTRY POINT
if __name__ == "__main__":
    asyncio.run(main())
