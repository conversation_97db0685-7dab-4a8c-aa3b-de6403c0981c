
# 🎓 Learning AI Agents with Streaming and Tools

This project is my learning journey with AI agents, streaming responses, and tool integration using the `openai-agents` library.

## 📚 What I'm Learning

This codebase demonstrates:
- **AI Agent Architecture**: How to create agents with instructions and tools
- **Function Tools**: Converting Python functions into AI-callable tools
- **Streaming Responses**: Real-time event processing from AI agents
- **Async Programming**: Modern Python patterns for AI applications
- **Event Handling**: Processing different types of streaming events

## 🛠️ Current Implementation

### My Agent Setup
```python
import asyncio
import random
from agents import Agent, Runner, ItemHelpers, function_tool

# Tool definition using decorator
@function_tool
def how_many_jokes():
    """Determine how many jokes to tell (between 1 and 3)."""
    num_jokes = random.randint(1, 3)
    return f"I should tell {num_jokes} joke(s)."

# Agent creation
agent = Agent(
    name="joke_teller",
    instructions="You are a helpful assistant. First, determine how many jokes to tell, then provide jokes.",
    tools=[how_many_jokes],
)
```

### Streaming Event Processing
```python
async def main():
    result = Runner.run_streamed(agent, input="Hello")

    async for event in result.stream_events():
        if hasattr(event, 'item'):
            if event.item.type == "tool_call_output_item":
                print(f"Tool output: {event.item.output}")
            elif event.item.type == "message_output_item":
                print(ItemHelpers.text_message_output(event.item))
```

## 🔧 Key Learning Points

### 1. Function Tool Decorator
The `@function_tool` decorator automatically:
- Converts Python functions into AI tools
- Extracts descriptions from docstrings
- Creates JSON schemas for parameters
- Makes functions callable by AI agents

**My tool schema generated:**
```json
{
  "properties": {},
  "title": "how_many_jokes_args",
  "type": "object",
  "additionalProperties": false,
  "required": []
}
```

### 2. Agent Components
An AI agent requires:
- **Name**: Unique identifier
- **Instructions**: System prompt defining behavior
- **Tools**: Functions the agent can call

### 3. Streaming Architecture
Benefits of streaming:
- Real-time response processing
- Better user experience
- Ability to handle long-running operations
- Event-driven architecture

### 4. Event Types Discovered
- `AgentUpdatedStreamEvent`: Agent configuration changes
- `RunItemStreamEvent`: Contains actual items (messages, tool calls)
  - `tool_call_output_item`: Results from tool execution
  - `message_output_item`: AI-generated text responses

## 🚀 Running the Code

### Prerequisites
```bash
# Install dependencies
uv sync

# Set OpenAI API key (required)
export OPENAI_API_KEY="your-api-key-here"
```

### Execute
```bash
uv run main.py
```

### Expected Flow (with working API key)
1. Agent receives input: "Hello"
2. Agent calls `how_many_jokes()` tool
3. Tool returns: "I should tell 2 joke(s)."
4. Agent generates 2 actual jokes
5. Events stream in real-time

## 📖 Learning Resources

### Original Reference
This code is based on learning from an original streaming agents example that demonstrates:
- Tool-agent interaction patterns
- Async event processing
- Real-time AI responses

### Key Concepts Mastered
- ✅ Function tool creation with decorators
- ✅ Agent initialization and configuration
- ✅ Streaming event handling
- ✅ Async programming patterns
- ✅ Error handling and debugging
- ✅ Tool schema generation

## 🎯 Next Learning Steps

Potential areas to explore:
1. **Multi-parameter tools** - Functions with complex inputs
2. **Tool chaining** - One tool calling another
3. **Error handling** - Graceful failure recovery
4. **Multiple agents** - Agent-to-agent communication
5. **Custom event processing** - Advanced streaming patterns

## 🔍 Debugging Notes

When learning, I encountered:
- Missing `Agent` import (fixed)
- Incorrect `FunctionTool` usage (learned about `@function_tool`)
- Missing agent `name` parameter (required field)
- Event structure differences (learned about `hasattr` checks)
- API key configuration (environment setup)

Each error was a learning opportunity to understand the library better!

---

*This README documents my learning process with AI agents and streaming. The code demonstrates core concepts even when API limitations prevent full execution.*
