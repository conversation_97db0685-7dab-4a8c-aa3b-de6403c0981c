import asyncio
import random
import os
from typing import List, Dict, Any
from agents import Agent, <PERSON>, ItemHel<PERSON>, function_tool

# Enhanced imports for Gemini integration
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    print("⚠️  google-generativeai not installed. Install with: pip install google-generativeai")

# ────────────────────────────────────────────────────────────────
# 🔧 GEMINI INTEGRATION HELPER
class GeminiHelper:
    """Helper class for Gemini API integration with enhanced features."""
    
    def __init__(self):
        self.api_key = os.getenv('GEMINI_API_KEY')
        self.model = None
        if GEMINI_AVAILABLE and self.api_key:
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel('gemini-pro')
            print("✅ Gemini API configured successfully!")
        else:
            print("❌ Gemini API not available (missing API key or library)")
    
    async def generate_jokes_with_gemini(self, topic: str, count: int) -> List[str]:
        """Use Gemini to generate fresh jokes about a topic."""
        if not self.model:
            return [f"Gemini not available. Here's a fallback joke about {topic}!"]
        
        try:
            prompt = f"Generate exactly {count} clean, funny jokes about {topic}. Return only the jokes, numbered 1-{count}."
            response = await asyncio.to_thread(self.model.generate_content, prompt)
            
            # Parse the response into individual jokes
            jokes_text = response.text.strip()
            jokes = [joke.strip() for joke in jokes_text.split('\n') if joke.strip() and any(char.isdigit() for char in joke[:3])]
            return jokes[:count] if jokes else [f"Generated joke about {topic} (parsing failed)"]
            
        except Exception as e:
            return [f"Gemini error: {str(e)[:50]}... Here's a backup joke about {topic}!"]

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the Gemini model."""
        return {
            "available": bool(self.model),
            "api_key_set": bool(self.api_key),
            "library_installed": GEMINI_AVAILABLE,
            "model_name": "gemini-pro" if self.model else None
        }

# Initialize Gemini helper
gemini_helper = GeminiHelper()

# ────────────────────────────────────────────────────────────────
# 📦 ENHANCED TOOLS WITH GEMINI INTEGRATION

@function_tool
def how_many_jokes():
    """Decide a random number of jokes to tell (between 1 and 4)."""
    return random.randint(1, 4)

@function_tool
def get_joke_topics() -> List[str]:
    """Get available joke topics."""
    return ["tech", "science", "animals", "food", "space", "random"]

@function_tool
def get_gemini_status() -> Dict[str, Any]:
    """Check if Gemini API is available and configured."""
    return gemini_helper.get_model_info()

@function_tool
def tell_jokes_static(topic: str, count: int) -> List[str]:
    """Return pre-written jokes about the given topic (fallback method)."""
    all_jokes = {
        "tech": [
            "Why do programmers prefer dark mode? Because light attracts bugs!",
            "There are only 10 types of people in the world: those who understand binary and those who don't.",
            "Why was the developer unhappy at their job? They wanted arrays.",
            "How many programmers does it take to change a light bulb? None, that's a hardware problem!"
        ],
        "science": [
            "Why can't you trust an atom? Because they make up everything!",
            "I told a chemistry joke once... there was no reaction.",
            "Why did the photon check a bag? It didn't – it was traveling light.",
            "What do you call a sleeping bull at the lab? A bulldozer!"
        ],
        "animals": [
            "What do you call a sleeping bull? A bulldozer!",
            "Why don't elephants use computers? They're afraid of the mouse!",
            "What do you call a fish wearing a crown? A king fish!",
            "Why don't cats play poker in the jungle? Too many cheetahs!"
        ],
        "food": [
            "Why did the cookie go to the doctor? Because it felt crumbly!",
            "What do you call a nosy pepper? Jalapeño business!",
            "Why don't eggs tell jokes? They'd crack each other up!",
            "What do you call a sad strawberry? A blueberry!"
        ]
    }
    jokes = all_jokes.get(topic.lower(), [f"No static jokes available for {topic}, but here's one: Why did the {topic} cross the road? To get to the other side!"])
    return jokes[:count]

@function_tool
async def tell_jokes_gemini(topic: str, count: int) -> List[str]:
    """Generate fresh jokes using Gemini AI about the given topic."""
    return await gemini_helper.generate_jokes_with_gemini(topic, count)

# ────────────────────────────────────────────────────────────────
# 🤖 ENHANCED AGENT DEFINITION WITH DUAL API SUPPORT
agent = Agent(
    name="gemini_joke_master",
    instructions=(
        "You're an advanced humorous assistant with dual capabilities. "
        "First, check Gemini status with 'get_gemini_status'. "
        "Then use 'how_many_jokes' to decide how many jokes to tell. "
        "If Gemini is available, use 'tell_jokes_gemini' for fresh AI-generated jokes. "
        "Otherwise, use 'tell_jokes_static' for pre-written jokes. "
        "You can also use 'get_joke_topics' to see available topics."
    ),
    tools=[how_many_jokes, get_joke_topics, get_gemini_status, tell_jokes_static, tell_jokes_gemini],
)

# ────────────────────────────────────────────────────────────────
# 🔄 ENHANCED MAIN EVENT LOOP WITH GEMINI INTEGRATION
async def main():
    print("\n🚀 Enhanced AI Agent with Gemini Integration and Multi-Tool Chaining\n")
    
    # Display configuration
    print("🔧 Configuration:")
    print(f"   └─ Tools Registered: {[tool.name for tool in agent.tools]}")
    print(f"   └─ Agent Name: {agent.name}")
    print(f"   └─ Gemini Status: {gemini_helper.get_model_info()}")
    
    print(f"\n🧠 Agent Instructions:")
    print(f"   {agent.instructions}")
    
    print(f"\n📋 Available Joke Topics: {['tech', 'science', 'animals', 'food', 'space', 'random']}")

    try:
        print(f"\n🎯 Starting agent with input: 'Tell me some tech jokes!'")
        result = Runner.run_streamed(agent, input="Tell me some tech jokes!")

        print(f"\n📡 Processing Stream Events:")
        async for event in result.stream_events():
            event_type = type(event).__name__
            print(f"\n   📡 Event: {event_type}")

            if hasattr(event, 'item'):
                item_type = event.item.type
                print(f"      └─ Item Type: {item_type}")

                if item_type == "tool_call_output_item":
                    print(f"      └─ 🔧 Tool Output: {event.item.output}")
                elif item_type == "message_output_item":
                    message = ItemHelpers.text_message_output(event.item)
                    print(f"      └─ 💬 AI Message:\n{message}")
            else:
                print("      └─ Other event structure (not an item)")

    except Exception as e:
        print(f"\n❌ Error: {type(e).__name__} - {str(e)}")
        print(f"\n🎓 Learning Summary - Even with errors, you learned:")
        print(f"   ✅ Gemini API integration patterns")
        print(f"   ✅ Dual API support (Gemini + fallback)")
        print(f"   ✅ Enhanced tool chaining")
        print(f"   ✅ Status checking tools")
        print(f"   ✅ Async programming with external APIs")
        print(f"   ✅ Error handling and graceful degradation")

# ────────────────────────────────────────────────────────────────
# 🟢 ENTRY POINT
if __name__ == "__main__":
    asyncio.run(main())
