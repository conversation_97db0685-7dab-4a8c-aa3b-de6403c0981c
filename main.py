import asyncio
import random
from agents import <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, function_tool

# Define a simple tool function using the decorator
@function_tool
def how_many_jokes():
    """Determine how many jokes to tell (between 1 and 3)."""
    num_jokes = random.randint(1, 3)
    return f"I should tell {num_jokes} joke(s)."

async def main():
    print("=== LEARNING: AI Agent with Streaming and Tools ===\n")

    print("1. Creating an AI Agent:")
    print("   - Name: 'joke_teller'")
    print("   - Instructions: Tell the agent what to do")
    print("   - Tools: Functions the agent can call")

    agent = Agent(
        name="joke_teller",
        instructions="You are a helpful assistant. First, determine how many jokes to tell, then provide jokes.",
        tools=[how_many_jokes],
    )
    print(f"   ✓ Agent created with {len(agent.tools)} tool(s)\n")

    print("2. Running the agent with streaming:")
    print("   - Input: 'Hello'")
    print("   - Streaming: See events as they happen in real-time")

    try:
        result = Runner.run_streamed(agent, input="Hello")

        print("   ✓ Agent started, processing events...\n")
        print("3. Stream Events (real-time processing):")

        async for event in result.stream_events():
            print(f"   📡 Event: {type(event).__name__}")

            if hasattr(event, 'item'):
                print(f"      └─ Item type: {event.item.type}")
                if event.item.type == "tool_call_output_item":
                    print(f"      └─ 🔧 Tool output: {event.item.output}")
                elif event.item.type == "message_output_item":
                    print(f"      └─ 💬 Message: {ItemHelpers.text_message_output(event.item)}")
            else:
                print(f"      └─ Details: {str(event)[:100]}...")
            print()

    except Exception as e:
        print(f"   ❌ Error occurred: {type(e).__name__}")
        print(f"      Message: {str(e)[:100]}...")
        print("\n=== LEARNING SUMMARY ===")
        print("Even though we got an error, you learned about:")
        print("✓ How to create function tools with @function_tool decorator")
        print("✓ How to create an AI agent with name, instructions, and tools")
        print("✓ How streaming works - events come in real-time")
        print("✓ Different event types: AgentUpdatedStreamEvent, RunItemStreamEvent")
        print("✓ How tools are automatically converted to JSON schema")
        print("✓ The agent lifecycle and error handling")

        print(f"\n🔧 Tool Details:")
        print(f"   Name: {how_many_jokes.name}")
        print(f"   Description: {how_many_jokes.description}")
        print(f"   Schema: {how_many_jokes.params_json_schema}")

if __name__ == "__main__":
    asyncio.run(main())